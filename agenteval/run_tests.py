#!/usr/bin/env python3
"""
Test runner script for Agent Evaluation Tool (Root Directory).

This script provides a convenient way to run tests from the project root directory.
It delegates to the actual test runner in the tests/ directory.
"""

import os
import sys
import subprocess
from pathlib import Path


def main():
    """Main function to delegate to the tests directory runner."""
    # Get the project root directory
    project_root = Path(__file__).parent
    tests_dir = project_root / "tests"
    test_runner = tests_dir / "run_tests.py"
    
    # Check if the test runner exists
    if not test_runner.exists():
        print("❌ Test runner not found at tests/run_tests.py")
        print("Please make sure the tests directory is properly set up.")
        sys.exit(1)
    
    # Change to project root directory
    os.chdir(project_root)
    
    # Run the test runner with all arguments passed through
    cmd = [sys.executable, str(test_runner)] + sys.argv[1:]
    
    try:
        result = subprocess.run(cmd, check=False)
        sys.exit(result.returncode)
    except KeyboardInterrupt:
        print("\n❌ Test execution interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Error running tests: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
