"""Task queue implementation for the Agent Evaluation Tool."""

import asyncio
import uuid
from typing import Dict, Any, Optional, List, Callable, Awaitable, Tuple
import logging
import time
from enum import Enum

logger = logging.getLogger(__name__)

class TaskStatus(str, Enum):
    """Task status enum."""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class Task:
    """Task representation."""
    
    def __init__(self, task_id: str, task_type: str, params: Dict[str, Any]):
        """Initialize a task.
        
        Args:
            task_id: Unique task ID.
            task_type: Type of task (benchmark name).
            params: Task parameters.
        """
        self.task_id = task_id
        self.task_type = task_type
        self.params = params
        self.status = TaskStatus.PENDING
        self.result = None
        self.error = None
        self.created_at = time.time()
        self.started_at = None
        self.completed_at = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert task to dictionary.
        
        Returns:
            Dictionary representation of the task.
        """
        return {
            "task_id": self.task_id,
            "task_type": self.task_type,
            "params": self.params,
            "status": self.status,
            "result": self.result,
            "error": self.error,
            "created_at": self.created_at,
            "started_at": self.started_at,
            "completed_at": self.completed_at
        }


class TaskQueue:
    """Asynchronous task queue."""
    
    def __init__(self, max_size: int = 100):
        """Initialize the task queue.
        
        Args:
            max_size: Maximum queue size.
        """
        self.queue = asyncio.Queue(maxsize=max_size)
        self.tasks: Dict[str, Task] = {}
        self._lock = asyncio.Lock()
    
    async def enqueue(self, task_type: str, params: Dict[str, Any]) -> str:
        """Enqueue a new task.
        
        Args:
            task_type: Type of task (benchmark name).
            params: Task parameters.
            
        Returns:
            Task ID.
        """
        task_id = str(uuid.uuid4())
        task = Task(task_id, task_type, params)
        
        async with self._lock:
            self.tasks[task_id] = task
        
        await self.queue.put(task_id)
        logger.info(f"Task {task_id} ({task_type}) enqueued")
        return task_id
    
    async def dequeue(self) -> Optional[str]:
        """Dequeue a task.
        
        Returns:
            Task ID or None if the queue is empty.
        """
        try:
            task_id = await self.queue.get()
            return task_id
        except asyncio.CancelledError:
            logger.warning("Dequeue operation cancelled")
            return None
    
    async def get_task(self, task_id: str) -> Optional[Task]:
        """Get a task by ID.
        
        Args:
            task_id: Task ID.
            
        Returns:
            Task or None if not found.
        """
        async with self._lock:
            return self.tasks.get(task_id)
    
    async def update_task_status(self, task_id: str, status: TaskStatus, 
                                result: Any = None, error: str = None) -> bool:
        """Update task status.
        
        Args:
            task_id: Task ID.
            status: New task status.
            result: Task result (if completed).
            error: Error message (if failed).
            
        Returns:
            True if the task was updated, False otherwise.
        """
        async with self._lock:
            task = self.tasks.get(task_id)
            if not task:
                logger.warning(f"Task {task_id} not found")
                return False
            
            task.status = status
            
            if status == TaskStatus.RUNNING and task.started_at is None:
                task.started_at = time.time()
            
            if status in (TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED):
                task.completed_at = time.time()
                
                if status == TaskStatus.COMPLETED:
                    task.result = result
                elif status == TaskStatus.FAILED:
                    task.error = error
            
            logger.info(f"Task {task_id} status updated to {status}")
            return True
    
    async def list_tasks(self, status: Optional[TaskStatus] = None) -> List[Dict[str, Any]]:
        """List all tasks, optionally filtered by status.
        
        Args:
            status: Filter tasks by status.
            
        Returns:
            List of tasks as dictionaries.
        """
        async with self._lock:
            if status:
                return [task.to_dict() for task in self.tasks.values() if task.status == status]
            return [task.to_dict() for task in self.tasks.values()]
    
    async def cancel_task(self, task_id: str) -> bool:
        """Cancel a pending task.
        
        Args:
            task_id: Task ID.
            
        Returns:
            True if the task was cancelled, False otherwise.
        """
        async with self._lock:
            task = self.tasks.get(task_id)
            if not task:
                logger.warning(f"Task {task_id} not found")
                return False
            
            if task.status == TaskStatus.PENDING:
                task.status = TaskStatus.CANCELLED
                task.completed_at = time.time()
                logger.info(f"Task {task_id} cancelled")
                return True
            
            logger.warning(f"Cannot cancel task {task_id} with status {task.status}")
            return False


# Global task queue instance
_task_queue_instance = None

def get_task_queue(max_size: int = 100) -> TaskQueue:
    """Get the global task queue instance.
    
    Args:
        max_size: Maximum queue size.
        
    Returns:
        The global task queue instance.
    """
    global _task_queue_instance
    if _task_queue_instance is None:
        _task_queue_instance = TaskQueue(max_size=max_size)
    return _task_queue_instance
