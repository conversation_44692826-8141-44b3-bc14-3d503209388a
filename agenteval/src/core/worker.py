"""Worker implementation for the Agent Evaluation Tool."""

import asyncio
import logging
import time
import traceback
import os
from typing import Dict, Any, List, Optional, Type, Set, Tuple
from collections import defaultdict

from src.core.queue import get_task_queue, TaskStatus, Task
from src.core.config import get_config
from src.adapters.base import BaseAdapter
from src.llm.client import LLMClient

logger = logging.getLogger(__name__)

class Worker:
    """Worker for processing evaluation tasks."""

    def __init__(self, worker_id: str, adapters: Dict[str, BaseAdapter],
                 max_concurrent_tasks: int = 5, timeout_seconds: int = 3600,
                 model_concurrency: Optional[Dict[str, int]] = None):
        """Initialize a worker.

        Args:
            worker_id: Unique worker ID.
            adapters: Dictionary mapping task types to adapters.
            max_concurrent_tasks: Maximum number of concurrent tasks.
            timeout_seconds: Task timeout in seconds.
            model_concurrency: Dictionary mapping model names to maximum concurrent tasks per model.
                               If None, no per-model limits are applied.
        """
        self.worker_id = worker_id
        self.adapters = adapters
        self.max_concurrent_tasks = max_concurrent_tasks
        self.timeout_seconds = timeout_seconds
        self.task_queue = get_task_queue()
        self.running = False
        self.tasks: Dict[str, asyncio.Task] = {}

        # Model concurrency limits
        self.model_concurrency = model_concurrency or {}

        # Track active tasks per model
        self.active_model_tasks: Dict[str, Set[str]] = defaultdict(set)

        # LLM client for potential use
        self.llm_client = LLMClient()

        # Statistics
        self.stats = {
            "tasks_completed": 0,
            "tasks_failed": 0,
            "tasks_cancelled": 0,
            "total_execution_time": 0.0,
            "model_stats": defaultdict(lambda: {
                "tasks_completed": 0,
                "tasks_failed": 0,
                "avg_execution_time": 0.0,
                "total_execution_time": 0.0
            })
        }

    async def start(self) -> None:
        """Start the worker."""
        if self.running:
            logger.warning(f"Worker {self.worker_id} is already running")
            return

        self.running = True
        logger.info(f"Worker {self.worker_id} started")

        try:
            await self._process_tasks()
        except Exception as e:
            logger.error(f"Worker {self.worker_id} encountered an error: {e}")
            self.running = False

    async def stop(self) -> None:
        """Stop the worker."""
        if not self.running:
            logger.warning(f"Worker {self.worker_id} is not running")
            return

        self.running = False
        logger.info(f"Worker {self.worker_id} stopping...")

        # Cancel all running tasks
        for task_id, task in self.tasks.items():
            if not task.done():
                task.cancel()
                await self.task_queue.update_task_status(
                    task_id, TaskStatus.CANCELLED, error="Worker stopped"
                )

        logger.info(f"Worker {self.worker_id} stopped")

    async def _process_tasks(self) -> None:
        """Process tasks from the queue."""
        while self.running:
            # Check if we can process more tasks
            if len(self.tasks) >= self.max_concurrent_tasks:
                # Wait for a task to complete
                done, _ = await asyncio.wait(
                    [task for task in self.tasks.values() if not task.done()],
                    return_when=asyncio.FIRST_COMPLETED
                )

                # Clean up completed tasks
                for task in done:
                    for task_id, t in self.tasks.items():
                        if t == task:
                            # Remove from active model tasks
                            self._remove_from_active_model_tasks(task_id)
                            # Remove from tasks
                            del self.tasks[task_id]
                            break

            # Get a new task from the queue
            task_id = await self.task_queue.dequeue()
            if not task_id:
                # If no task is available, wait a bit to avoid busy-waiting
                await asyncio.sleep(0.1)
                continue

            # Get the task details
            task = await self.task_queue.get_task(task_id)
            if not task:
                logger.warning(f"Task {task_id} not found")
                continue

            # Check if the task type is supported
            if task.task_type not in self.adapters:
                await self.task_queue.update_task_status(
                    task_id, TaskStatus.FAILED,
                    error=f"Unsupported task type: {task.task_type}"
                )
                continue

            # Check model-specific concurrency limits
            model = task.params.get("model", "default")
            if model in self.model_concurrency:
                max_model_tasks = self.model_concurrency[model]
                current_model_tasks = len(self.active_model_tasks[model])

                if current_model_tasks >= max_model_tasks:
                    logger.debug(f"Model {model} at concurrency limit ({current_model_tasks}/{max_model_tasks}), re-queueing task {task_id}")
                    # Re-queue the task for later processing
                    await self.task_queue.queue.put(task_id)
                    continue

            # Update task status to running
            await self.task_queue.update_task_status(task_id, TaskStatus.RUNNING)

            # Add to active model tasks
            self._add_to_active_model_tasks(task_id, model)

            # Process the task
            self.tasks[task_id] = asyncio.create_task(
                self._execute_task(task_id, task.task_type, task.params)
            )

    def _add_to_active_model_tasks(self, task_id: str, model: str) -> None:
        """Add a task to the active model tasks.

        Args:
            task_id: Task ID.
            model: Model name.
        """
        self.active_model_tasks[model].add(task_id)
        logger.debug(f"Added task {task_id} to active model tasks for {model} (now {len(self.active_model_tasks[model])})")

    def _remove_from_active_model_tasks(self, task_id: str) -> None:
        """Remove a task from the active model tasks.

        Args:
            task_id: Task ID.
        """
        for model, tasks in self.active_model_tasks.items():
            if task_id in tasks:
                tasks.remove(task_id)
                logger.debug(f"Removed task {task_id} from active model tasks for {model} (now {len(tasks)})")
                break

    async def _execute_task(self, task_id: str, task_type: str, params: Dict[str, Any]) -> None:
        """Execute a task.

        Args:
            task_id: Task ID.
            task_type: Task type.
            params: Task parameters.
        """
        model = params.get("model", "default")
        start_time = time.time()
        logger.info(f"Executing task {task_id} ({task_type}) with model {model}")

        try:
            # Get the adapter for the task type
            adapter = self.adapters[task_type]

            # Execute the task with timeout
            result = await asyncio.wait_for(
                adapter.execute(params),
                timeout=self.timeout_seconds
            )

            # Calculate execution time
            execution_time = time.time() - start_time

            # Update statistics
            self.stats["tasks_completed"] += 1
            self.stats["total_execution_time"] += execution_time

            # Update model-specific statistics
            model_stats = self.stats["model_stats"][model]
            model_stats["tasks_completed"] += 1
            model_stats["total_execution_time"] += execution_time

            # Calculate average execution time
            if model_stats["tasks_completed"] > 0:
                model_stats["avg_execution_time"] = (
                    model_stats["total_execution_time"] / model_stats["tasks_completed"]
                )

            # Add execution time to result
            if isinstance(result, dict):
                result["execution_time"] = execution_time

            # Update task status to completed
            await self.task_queue.update_task_status(
                task_id, TaskStatus.COMPLETED, result=result
            )

            logger.info(f"Task {task_id} completed successfully in {execution_time:.2f}s")

        except asyncio.TimeoutError:
            execution_time = time.time() - start_time
            logger.error(f"Task {task_id} timed out after {self.timeout_seconds} seconds")

            # Update statistics
            self.stats["tasks_failed"] += 1
            self.stats["model_stats"][model]["tasks_failed"] += 1

            await self.task_queue.update_task_status(
                task_id, TaskStatus.FAILED,
                error=f"Task timed out after {self.timeout_seconds} seconds"
            )

        except asyncio.CancelledError:
            execution_time = time.time() - start_time
            logger.warning(f"Task {task_id} was cancelled after {execution_time:.2f}s")

            # Update statistics
            self.stats["tasks_cancelled"] += 1

            await self.task_queue.update_task_status(
                task_id, TaskStatus.CANCELLED,
                error="Task was cancelled"
            )

        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"Task {task_id} failed after {execution_time:.2f}s: {e}")
            logger.error(traceback.format_exc())

            # Update statistics
            self.stats["tasks_failed"] += 1
            self.stats["model_stats"][model]["tasks_failed"] += 1

            # Create a detailed error message
            error_message = f"{type(e).__name__}: {str(e)}"

            await self.task_queue.update_task_status(
                task_id, TaskStatus.FAILED,
                error=error_message
            )


class WorkerPool:
    """Pool of workers for processing evaluation tasks."""

    def __init__(self, adapters: Dict[str, BaseAdapter], worker_count: int = 4):
        """Initialize the worker pool.

        Args:
            adapters: Dictionary mapping task types to adapters.
            worker_count: Number of workers to create.
        """
        self.adapters = adapters
        self.worker_count = worker_count
        self.workers: List[Worker] = []
        self.running = False
        self.stats_task = None

    async def start(self) -> None:
        """Start the worker pool."""
        if self.running:
            logger.warning("Worker pool is already running")
            return

        self.running = True
        logger.info(f"Starting worker pool with {self.worker_count} workers")

        config = get_config()
        max_concurrent_tasks = config.get("worker.max_concurrent_tasks", 5)
        timeout_seconds = config.get("worker.timeout_seconds", 3600)

        # Get model concurrency limits from config
        model_concurrency = self._get_model_concurrency_config(config)

        # Create and start workers
        for i in range(self.worker_count):
            worker = Worker(
                worker_id=f"worker-{i+1}",
                adapters=self.adapters,
                max_concurrent_tasks=max_concurrent_tasks,
                timeout_seconds=timeout_seconds,
                model_concurrency=model_concurrency
            )
            self.workers.append(worker)
            asyncio.create_task(worker.start())

        # Start stats collection task
        if config.get("worker.collect_stats", True):
            self.stats_task = asyncio.create_task(self._collect_stats())

        logger.info("Worker pool started")

    def _get_model_concurrency_config(self, config) -> Dict[str, int]:
        """Get model concurrency configuration.

        Args:
            config: Configuration object.

        Returns:
            Dictionary mapping model names to maximum concurrent tasks.
        """
        model_concurrency = {}

        # Get model concurrency from config
        models_config = config.get("worker.model_concurrency", {})

        # If it's a dictionary, use it directly
        if isinstance(models_config, dict):
            model_concurrency = models_config
        # Otherwise, try to parse it from the config
        else:
            for model_config in config.get("llm.endpoints", []):
                model_name = model_config.get("name")
                if model_name and "max_concurrent" in model_config:
                    model_concurrency[model_name] = model_config["max_concurrent"]

        if model_concurrency:
            logger.info(f"Model concurrency limits: {model_concurrency}")

        return model_concurrency

    async def _collect_stats(self) -> None:
        """Collect and log worker statistics periodically."""
        try:
            while self.running:
                await asyncio.sleep(60)  # Collect stats every minute

                # Aggregate stats from all workers
                total_stats = {
                    "tasks_completed": 0,
                    "tasks_failed": 0,
                    "tasks_cancelled": 0,
                    "total_execution_time": 0.0,
                    "model_stats": defaultdict(lambda: {
                        "tasks_completed": 0,
                        "tasks_failed": 0,
                        "avg_execution_time": 0.0,
                        "total_execution_time": 0.0
                    })
                }

                for worker in self.workers:
                    total_stats["tasks_completed"] += worker.stats["tasks_completed"]
                    total_stats["tasks_failed"] += worker.stats["tasks_failed"]
                    total_stats["tasks_cancelled"] += worker.stats["tasks_cancelled"]
                    total_stats["total_execution_time"] += worker.stats["total_execution_time"]

                    # Aggregate model stats
                    for model, stats in worker.stats["model_stats"].items():
                        model_stats = total_stats["model_stats"][model]
                        model_stats["tasks_completed"] += stats["tasks_completed"]
                        model_stats["tasks_failed"] += stats["tasks_failed"]
                        model_stats["total_execution_time"] += stats["total_execution_time"]

                # Calculate average execution times
                for model, stats in total_stats["model_stats"].items():
                    if stats["tasks_completed"] > 0:
                        stats["avg_execution_time"] = (
                            stats["total_execution_time"] / stats["tasks_completed"]
                        )

                # Log stats
                logger.info(f"Worker pool stats: completed={total_stats['tasks_completed']}, "
                           f"failed={total_stats['tasks_failed']}, "
                           f"cancelled={total_stats['tasks_cancelled']}")

                # Log model-specific stats
                for model, stats in total_stats["model_stats"].items():
                    if stats["tasks_completed"] > 0 or stats["tasks_failed"] > 0:
                        logger.info(f"Model {model} stats: completed={stats['tasks_completed']}, "
                                   f"failed={stats['tasks_failed']}, "
                                   f"avg_time={stats['avg_execution_time']:.2f}s")

        except asyncio.CancelledError:
            logger.debug("Stats collection task cancelled")
        except Exception as e:
            logger.error(f"Error in stats collection: {e}")

    async def stop(self) -> None:
        """Stop the worker pool."""
        if not self.running:
            logger.warning("Worker pool is not running")
            return

        self.running = False
        logger.info("Stopping worker pool...")

        # Cancel stats collection task
        if self.stats_task and not self.stats_task.done():
            self.stats_task.cancel()
            try:
                await self.stats_task
            except asyncio.CancelledError:
                pass

        # Stop all workers
        await asyncio.gather(*[worker.stop() for worker in self.workers])
        self.workers = []

        logger.info("Worker pool stopped")

    async def get_stats(self) -> Dict[str, Any]:
        """Get worker pool statistics.

        Returns:
            Dictionary with worker pool statistics.
        """
        # Aggregate stats from all workers
        total_stats = {
            "tasks_completed": 0,
            "tasks_failed": 0,
            "tasks_cancelled": 0,
            "total_execution_time": 0.0,
            "model_stats": {}
        }

        for worker in self.workers:
            total_stats["tasks_completed"] += worker.stats["tasks_completed"]
            total_stats["tasks_failed"] += worker.stats["tasks_failed"]
            total_stats["tasks_cancelled"] += worker.stats["tasks_cancelled"]
            total_stats["total_execution_time"] += worker.stats["total_execution_time"]

            # Aggregate model stats
            for model, stats in worker.stats["model_stats"].items():
                if model not in total_stats["model_stats"]:
                    total_stats["model_stats"][model] = {
                        "tasks_completed": 0,
                        "tasks_failed": 0,
                        "avg_execution_time": 0.0,
                        "total_execution_time": 0.0
                    }

                model_stats = total_stats["model_stats"][model]
                model_stats["tasks_completed"] += stats["tasks_completed"]
                model_stats["tasks_failed"] += stats["tasks_failed"]
                model_stats["total_execution_time"] += stats["total_execution_time"]

        # Calculate average execution times
        for model, stats in total_stats["model_stats"].items():
            if stats["tasks_completed"] > 0:
                stats["avg_execution_time"] = (
                    stats["total_execution_time"] / stats["tasks_completed"]
                )

        return total_stats


# Global worker pool instance
_worker_pool_instance = None

def get_worker_pool(adapters: Dict[str, BaseAdapter] = None, worker_count: int = None) -> WorkerPool:
    """Get the global worker pool instance.

    Args:
        adapters: Dictionary mapping task types to adapters.
        worker_count: Number of workers to create.

    Returns:
        The global worker pool instance.
    """
    global _worker_pool_instance

    if _worker_pool_instance is None and adapters is not None:
        config = get_config()
        if worker_count is None:
            worker_count = config.get("queue.worker_count", 4)

        _worker_pool_instance = WorkerPool(adapters=adapters, worker_count=worker_count)

    return _worker_pool_instance

async def get_worker_stats() -> Dict[str, Any]:
    """Get worker pool statistics.

    Returns:
        Dictionary with worker pool statistics or None if no worker pool is running.
    """
    worker_pool = get_worker_pool()
    if worker_pool and worker_pool.running:
        return await worker_pool.get_stats()
    return None
