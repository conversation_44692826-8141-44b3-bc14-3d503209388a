# Agent Evaluation Tool - 测试文档

本文档说明如何运行 Agent Evaluation Tool 的单元测试，特别是 FastAPI 和任务队列相关的功能测试。

## 测试概述

当前的测试套件主要覆盖以下功能：

### 1. FastAPI 相关测试
- **API 路由测试**：测试所有 REST API 端点
- **API 模型验证**：测试请求/响应模型的数据验证
- **错误处理测试**：测试各种错误情况的处理

### 2. 任务队列相关测试
- **任务入队/出队**：测试任务的基本队列操作
- **任务状态管理**：测试任务状态的更新和查询
- **并发安全性**：测试多线程环境下的队列操作

### 3. 工作器相关测试
- **工作器生命周期**：测试工作器的启动、停止
- **任务执行流程**：测试任务的完整执行过程
- **工作器池管理**：测试工作器池的管理功能

### 4. 集成测试
- **完整任务生命周期**：从任务创建到完成的端到端测试
- **API 与队列集成**：测试 API 层与队列系统的集成

## 安装测试依赖

在运行测试之前，需要安装测试相关的依赖：

```bash
# 方法1：使用测试运行脚本自动安装
python run_tests.py --install

# 方法2：手动安装
pip install pytest pytest-asyncio pytest-mock httpx
```

## 运行测试

### 使用测试运行脚本（推荐）

我们提供了一个便捷的测试运行脚本 `run_tests.py`：

```bash
# 检查测试环境
python run_tests.py --check

# 运行 API 和队列测试
python run_tests.py --api-queue

# 运行所有测试
python run_tests.py --all

# 运行测试并生成覆盖率报告
python run_tests.py --coverage
```

### 直接使用 pytest

```bash
# 运行特定的测试文件
pytest test_api_and_queue.py -v

# 运行所有测试
pytest -v

# 运行特定的测试类
pytest test_api_and_queue.py::TestTaskQueue -v

# 运行特定的测试方法
pytest test_api_and_queue.py::TestTaskQueue::test_enqueue_task -v

# 运行测试并显示覆盖率
pytest --cov=src --cov-report=html --cov-report=term-missing
```

## 测试文件结构

```
.
├── test_api_and_queue.py      # 主要测试文件
├── pytest.ini                # pytest 配置文件
├── run_tests.py              # 测试运行脚本
├── TEST_README.md            # 测试文档（本文件）
└── requirements.txt          # 包含测试依赖
```

## 测试类说明

### TestTaskQueue
测试任务队列的核心功能：
- `test_enqueue_task`: 测试任务入队
- `test_dequeue_task`: 测试任务出队
- `test_update_task_status`: 测试任务状态更新
- `test_list_tasks`: 测试任务列表查询
- `test_queue_size_limit`: 测试队列大小限制

### TestWorker
测试工作器功能：
- `test_worker_initialization`: 测试工作器初始化
- `test_worker_can_process_task`: 测试任务处理能力检查
- `test_worker_stats`: 测试工作器统计信息

### TestWorkerPool
测试工作器池功能：
- `test_worker_pool_initialization`: 测试工作器池初始化
- `test_worker_pool_stats`: 测试工作器池统计信息

### TestAPIModels
测试 API 数据模型：
- `test_task_request_validation`: 测试任务请求模型验证
- `test_batch_task_request_validation`: 测试批量任务请求验证
- `test_task_response_model`: 测试任务响应模型

### TestAPIRoutes
测试 API 路由：
- `test_create_task_endpoint`: 测试创建任务端点
- `test_create_batch_tasks_endpoint`: 测试批量创建任务端点
- `test_get_task_endpoint`: 测试获取任务端点
- `test_get_task_not_found`: 测试任务不存在的情况
- `test_list_tasks_endpoint`: 测试任务列表端点

### TestIntegration
集成测试：
- `test_full_task_lifecycle`: 测试完整的任务生命周期
- `test_concurrent_task_operations`: 测试并发任务操作

## 测试配置

测试配置在 `pytest.ini` 文件中定义：

- **异步支持**：自动处理 asyncio 测试
- **输出格式**：详细输出，短格式错误信息
- **标记系统**：支持按类型运行测试（unit, integration, api, queue, worker）
- **超时设置**：测试超时时间为 300 秒
- **日志配置**：启用测试期间的日志输出

## 常见问题

### 1. 导入错误
如果遇到模块导入错误，确保：
- 当前工作目录是项目根目录
- `src` 目录在 Python 路径中
- 所有必要的依赖都已安装

### 2. 异步测试问题
如果异步测试失败，确保：
- 安装了 `pytest-asyncio`
- 测试函数使用了 `@pytest.mark.asyncio` 装饰器

### 3. Mock 相关问题
如果 mock 测试失败，确保：
- 安装了 `pytest-mock`
- Mock 对象的配置正确

## 扩展测试

要添加新的测试：

1. **添加新的测试类**：在 `test_api_and_queue.py` 中添加新的测试类
2. **添加新的测试文件**：创建新的 `test_*.py` 文件
3. **添加测试标记**：在 `pytest.ini` 中添加新的标记
4. **更新测试脚本**：在 `run_tests.py` 中添加新的测试运行选项

## 持续集成

这些测试可以轻松集成到 CI/CD 流水线中：

```yaml
# GitHub Actions 示例
- name: Run tests
  run: |
    python run_tests.py --install
    python run_tests.py --all --coverage
```

## 性能测试

当前的测试主要关注功能正确性。如需性能测试，可以：

1. 添加性能基准测试
2. 使用 `pytest-benchmark` 插件
3. 测试高并发场景下的系统表现

## 总结

这个测试套件为 Agent Evaluation Tool 的核心功能提供了全面的测试覆盖，确保 FastAPI 和任务队列系统的稳定性和正确性。通过运行这些测试，可以验证系统的各个组件是否按预期工作。
