# Agent Evaluation Tool - 测试套件

这个文件夹包含了 Agent Evaluation Tool 的完整测试套件。

## 文件结构

```
tests/
├── __init__.py                 # 测试包初始化文件
├── conftest.py                 # 共享的测试配置和 fixtures
├── pytest.ini                 # pytest 配置文件
├── run_tests.py               # 测试运行脚本
├── test_api_and_queue.py      # FastAPI 和任务队列测试
├── README.md                  # 本文件
├── TEST_README.md             # 详细测试文档
└── TEST_RESULTS.md            # 测试结果报告
```

## 快速开始

### 1. 安装测试依赖

```bash
# 从项目根目录运行
python tests/run_tests.py --install
```

### 2. 检查测试环境

```bash
python tests/run_tests.py --check
```

### 3. 运行测试

```bash
# 运行 API 和队列测试
python tests/run_tests.py --api-queue

# 运行所有测试
python tests/run_tests.py --all

# 运行测试并生成覆盖率报告
python tests/run_tests.py --coverage
```

## 直接使用 pytest

```bash
# 从项目根目录运行所有测试
python -m pytest tests/ -v

# 运行特定测试文件
python -m pytest tests/test_api_and_queue.py -v

# 运行特定测试类
python -m pytest tests/test_api_and_queue.py::TestTaskQueue -v

# 运行特定测试方法
python -m pytest tests/test_api_and_queue.py::TestTaskQueue::test_enqueue_task -v

# 按标记运行测试
python -m pytest tests/ -m "api" -v
python -m pytest tests/ -m "queue" -v
python -m pytest tests/ -m "unit" -v
python -m pytest tests/ -m "integration" -v

# 运行测试并生成覆盖率报告
python -m pytest tests/ --cov=src --cov-report=html --cov-report=term-missing
```

## 测试标记

测试使用以下标记进行分类：

- `@pytest.mark.unit`: 单元测试
- `@pytest.mark.integration`: 集成测试
- `@pytest.mark.api`: API 相关测试
- `@pytest.mark.queue`: 队列相关测试
- `@pytest.mark.worker`: 工作器相关测试
- `@pytest.mark.slow`: 慢速测试

## 测试配置

### conftest.py

包含共享的测试配置和 fixtures：

- `app`: FastAPI 应用实例
- `client`: 同步测试客户端
- `async_client`: 异步测试客户端
- `task_queue`: 任务队列实例
- `mock_adapters`: 模拟适配器
- `worker`: 工作器实例
- `worker_pool`: 工作器池实例
- `sample_task_data`: 示例任务数据
- `sample_task_list`: 示例任务列表

### pytest.ini

包含 pytest 的配置选项：

- 异步测试支持
- 测试发现规则
- 输出格式配置
- 标记定义
- 日志配置

## 添加新测试

### 1. 创建新的测试文件

```python
# tests/test_new_feature.py
import pytest
from src.new_feature import NewFeature

class TestNewFeature:
    """Test cases for new feature."""
    
    def test_new_functionality(self):
        """Test new functionality."""
        feature = NewFeature()
        result = feature.do_something()
        assert result is not None
```

### 2. 使用共享 fixtures

```python
def test_with_shared_fixture(self, task_queue, mock_adapters):
    """Test using shared fixtures from conftest.py."""
    # 使用 task_queue 和 mock_adapters
    pass
```

### 3. 添加新的 fixtures

如果需要新的 fixtures，可以在 `conftest.py` 中添加：

```python
@pytest.fixture
def new_fixture():
    """New fixture for testing."""
    return SomeTestObject()
```

## 测试最佳实践

1. **测试命名**: 使用描述性的测试名称
2. **测试隔离**: 每个测试应该独立运行
3. **使用 fixtures**: 利用 fixtures 来设置测试数据
4. **Mock 外部依赖**: 使用 mock 对象模拟外部服务
5. **异步测试**: 使用 `@pytest.mark.asyncio` 装饰异步测试
6. **测试标记**: 为测试添加适当的标记

## 持续集成

这些测试可以轻松集成到 CI/CD 流水线中：

```yaml
# GitHub Actions 示例
- name: Install dependencies
  run: |
    python -m pip install --upgrade pip
    pip install -r requirements.txt

- name: Run tests
  run: |
    python tests/run_tests.py --all --coverage
```

## 故障排除

### 常见问题

1. **导入错误**: 确保从项目根目录运行测试
2. **异步测试失败**: 检查是否使用了正确的异步装饰器
3. **Mock 问题**: 验证 mock 对象的配置是否正确

### 调试测试

```bash
# 运行单个测试并显示详细输出
python -m pytest tests/test_api_and_queue.py::TestTaskQueue::test_enqueue_task -v -s

# 运行测试并在失败时进入调试器
python -m pytest tests/ --pdb

# 运行测试并显示最慢的 10 个测试
python -m pytest tests/ --durations=10
```

## 贡献

在添加新功能时，请确保：

1. 为新功能编写相应的测试
2. 确保所有现有测试仍然通过
3. 更新相关的文档
4. 遵循现有的测试模式和约定
