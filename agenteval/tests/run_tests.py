#!/usr/bin/env python3
"""
Test runner script for Agent Evaluation Tool.

This script provides convenient ways to run different types of tests.
"""

import os
import sys
import subprocess
import argparse
from pathlib import Path


def run_command(cmd, description=""):
    """Run a command and handle errors."""
    print(f"\n{'='*60}")
    if description:
        print(f"Running: {description}")
    print(f"Command: {' '.join(cmd)}")
    print(f"{'='*60}")

    try:
        result = subprocess.run(cmd, check=True, capture_output=False)
        print(f"\n✓ {description or 'Command'} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"\n✗ {description or 'Command'} failed with exit code {e.returncode}")
        return False
    except FileNotFoundError:
        print(f"\n✗ Command not found: {cmd[0]}")
        print("Make sure pytest is installed: pip install pytest pytest-asyncio pytest-mock")
        return False


def install_test_dependencies():
    """Install test dependencies."""
    print("Installing test dependencies...")

    dependencies = [
        "pytest>=7.3.1",
        "pytest-asyncio>=0.21.0",
        "pytest-mock>=3.10.0",
        "httpx>=0.24.0"
    ]

    for dep in dependencies:
        cmd = [sys.executable, "-m", "pip", "install", dep]
        if not run_command(cmd, f"Installing {dep}"):
            return False

    return True


def run_api_and_queue_tests():
    """Run API and queue tests."""
    cmd = [
        sys.executable, "-m", "pytest",
        "tests/test_api_and_queue.py",
        "-v",
        "--tb=short"
    ]
    return run_command(cmd, "API and Queue Tests")


def run_unit_tests():
    """Run all unit tests."""
    cmd = [
        sys.executable, "-m", "pytest",
        "tests/",
        "-m", "unit",
        "-v"
    ]
    return run_command(cmd, "Unit Tests")


def run_integration_tests():
    """Run integration tests."""
    cmd = [
        sys.executable, "-m", "pytest",
        "tests/",
        "-m", "integration",
        "-v"
    ]
    return run_command(cmd, "Integration Tests")


def run_all_tests():
    """Run all tests."""
    cmd = [
        sys.executable, "-m", "pytest",
        "tests/",
        "-v",
        "--tb=short"
    ]
    return run_command(cmd, "All Tests")


def run_tests_with_coverage():
    """Run tests with coverage report."""
    # First check if pytest-cov is available
    try:
        import pytest_cov
        cmd = [
            sys.executable, "-m", "pytest",
            "tests/",
            "--cov=src",
            "--cov-report=html",
            "--cov-report=term-missing",
            "-v"
        ]
        return run_command(cmd, "Tests with Coverage")
    except ImportError:
        print("pytest-cov not installed. Installing...")
        install_cmd = [sys.executable, "-m", "pip", "install", "pytest-cov"]
        if run_command(install_cmd, "Installing pytest-cov"):
            cmd = [
                sys.executable, "-m", "pytest",
                "tests/",
                "--cov=src",
                "--cov-report=html",
                "--cov-report=term-missing",
                "-v"
            ]
            return run_command(cmd, "Tests with Coverage")
        return False


def check_test_environment():
    """Check if the test environment is properly set up."""
    print("Checking test environment...")

    # Check if pytest is available
    try:
        import pytest
        print(f"✓ pytest {pytest.__version__} is available")
    except ImportError:
        print("✗ pytest is not installed")
        return False

    # Check if test files exist
    test_files = ["tests/test_api_and_queue.py", "tests/pytest.ini", "tests/conftest.py"]
    for test_file in test_files:
        if Path(test_file).exists():
            print(f"✓ {test_file} exists")
        else:
            print(f"✗ {test_file} not found")
            return False

    # Check if source code is available
    src_path = Path("src")
    if src_path.exists():
        print(f"✓ Source code directory exists")
    else:
        print(f"✗ Source code directory not found")
        return False

    print("\n✓ Test environment looks good!")
    return True


def main():
    """Main function."""
    parser = argparse.ArgumentParser(
        description="Test runner for Agent Evaluation Tool",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python run_tests.py --check          # Check test environment
  python run_tests.py --install        # Install test dependencies
  python run_tests.py --api-queue      # Run API and queue tests
  python run_tests.py --unit           # Run unit tests
  python run_tests.py --integration    # Run integration tests
  python run_tests.py --all            # Run all tests
  python run_tests.py --coverage       # Run tests with coverage
        """
    )

    parser.add_argument("--check", action="store_true",
                       help="Check test environment")
    parser.add_argument("--install", action="store_true",
                       help="Install test dependencies")
    parser.add_argument("--api-queue", action="store_true",
                       help="Run API and queue tests")
    parser.add_argument("--unit", action="store_true",
                       help="Run unit tests")
    parser.add_argument("--integration", action="store_true",
                       help="Run integration tests")
    parser.add_argument("--all", action="store_true",
                       help="Run all tests")
    parser.add_argument("--coverage", action="store_true",
                       help="Run tests with coverage report")

    args = parser.parse_args()

    # If no arguments provided, show help
    if not any(vars(args).values()):
        parser.print_help()
        return

    success = True

    if args.check:
        success &= check_test_environment()

    if args.install:
        success &= install_test_dependencies()

    if args.api_queue:
        success &= run_api_and_queue_tests()

    if args.unit:
        success &= run_unit_tests()

    if args.integration:
        success &= run_integration_tests()

    if args.all:
        success &= run_all_tests()

    if args.coverage:
        success &= run_tests_with_coverage()

    if success:
        print(f"\n{'='*60}")
        print("✓ All operations completed successfully!")
        print(f"{'='*60}")
    else:
        print(f"\n{'='*60}")
        print("✗ Some operations failed!")
        print(f"{'='*60}")
        sys.exit(1)


if __name__ == "__main__":
    main()
