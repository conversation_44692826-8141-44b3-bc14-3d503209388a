"""
Shared test configuration and fixtures for Agent Evaluation Tool tests.

This module contains pytest fixtures and configuration that are shared
across multiple test modules.
"""

import pytest
import pytest_asyncio
import asyncio
import sys
from pathlib import Path
from unittest.mock import Mock, AsyncMock
from typing import Dict, Any
from fastapi import <PERSON><PERSON><PERSON>
from fastapi.testclient import TestClient
from httpx import AsyncClient, ASGITransport

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from src.api.routes import router
from src.api.models import TaskStatus
from src.core.queue import TaskQueue
from src.core.worker import Worker, WorkerPool


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def app():
    """Create FastAPI test application."""
    app = FastAPI()
    app.include_router(router, prefix="/api")
    return app


@pytest.fixture
def client(app):
    """Create synchronous test client."""
    return TestClient(app)


@pytest_asyncio.fixture
async def async_client(app):
    """Create async test client."""
    async with AsyncClient(transport=ASGITransport(app=app), base_url="http://test") as ac:
        yield ac


@pytest.fixture
def task_queue():
    """Create a fresh task queue for testing."""
    return TaskQueue(max_size=10)


@pytest.fixture
def mock_adapters():
    """Create mock adapters for testing."""
    mock_adapter = Mock()
    mock_adapter.evaluate = AsyncMock(return_value={"score": 0.85, "details": "test result"})
    mock_adapter.execute = AsyncMock(return_value={"score": 0.85, "details": "test result"})
    mock_adapter.get_tasks = Mock(return_value=["task1", "task2"])
    mock_adapter.list_tasks = AsyncMock(return_value=[
        {"id": "task1", "name": "Task 1", "description": "Test task 1"},
        {"id": "task2", "name": "Task 2", "description": "Test task 2"}
    ])
    mock_adapter.get_task_details = AsyncMock(return_value={
        "id": "task1", 
        "name": "Task 1", 
        "description": "Test task 1"
    })
    mock_adapter.get_info = Mock(return_value={
        "name": "test_benchmark",
        "description": "Test benchmark",
        "tasks": [{"name": "task1"}, {"name": "task2"}]
    })
    
    return {
        "test_benchmark": mock_adapter,
        "tau_bench": mock_adapter,
        "bfc": mock_adapter,
        "gaia": mock_adapter
    }


@pytest.fixture
def worker(mock_adapters):
    """Create a test worker."""
    return Worker(
        worker_id="test-worker",
        adapters=mock_adapters,
        max_concurrent_tasks=2,
        timeout_seconds=30
    )


@pytest.fixture
def worker_pool(mock_adapters):
    """Create a test worker pool."""
    return WorkerPool(adapters=mock_adapters, worker_count=2)


@pytest.fixture
def sample_task_data():
    """Sample task data for testing."""
    return {
        "task_id": "test-task-123",
        "task_type": "tau_bench",
        "params": {"model": "gpt-4", "task": "test_task"},
        "status": TaskStatus.PENDING,
        "result": None,
        "error": None,
        "created_at": 1234567890.0,
        "started_at": None,
        "completed_at": None
    }


@pytest.fixture
def sample_task_list():
    """Sample task list for testing."""
    return [
        {
            "task_id": "task-1",
            "task_type": "tau_bench",
            "params": {"model": "gpt-4"},
            "status": TaskStatus.COMPLETED,
            "result": {"score": 0.9},
            "error": None,
            "created_at": 1234567890.0,
            "started_at": 1234567891.0,
            "completed_at": 1234567900.0
        },
        {
            "task_id": "task-2",
            "task_type": "bfc",
            "params": {"model": "claude-3-sonnet"},
            "status": TaskStatus.RUNNING,
            "result": None,
            "error": None,
            "created_at": 1234567895.0,
            "started_at": 1234567896.0,
            "completed_at": None
        }
    ]


# Test configuration
pytest_plugins = ["pytest_asyncio"]

# Configure asyncio mode
def pytest_configure(config):
    """Configure pytest settings."""
    config.addinivalue_line(
        "markers", "slow: marks tests as slow (deselect with '-m \"not slow\"')"
    )
    config.addinivalue_line(
        "markers", "integration: marks tests as integration tests"
    )
    config.addinivalue_line(
        "markers", "unit: marks tests as unit tests"
    )
    config.addinivalue_line(
        "markers", "api: marks tests as API tests"
    )
    config.addinivalue_line(
        "markers", "queue: marks tests as queue tests"
    )
    config.addinivalue_line(
        "markers", "worker: marks tests as worker tests"
    )


def pytest_collection_modifyitems(config, items):
    """Modify test collection to add markers automatically."""
    for item in items:
        # Add markers based on test file names
        if "test_api" in item.nodeid:
            item.add_marker(pytest.mark.api)
        if "test_queue" in item.nodeid:
            item.add_marker(pytest.mark.queue)
        if "test_worker" in item.nodeid:
            item.add_marker(pytest.mark.worker)
        if "integration" in item.nodeid.lower():
            item.add_marker(pytest.mark.integration)
        else:
            item.add_marker(pytest.mark.unit)


# Async test configuration
@pytest.fixture(scope="session")
def anyio_backend():
    """Configure anyio backend for async tests."""
    return "asyncio"
