"""
Agent Evaluation Tool - Test Suite

This package contains all test modules for the Agent Evaluation Tool.

Test Structure:
- test_api_and_queue.py: Tests for FastAPI routes and task queue functionality
- conftest.py: Shared test fixtures and configuration
- utils/: Test utility functions and helpers

Usage:
    # Run all tests
    python -m pytest tests/

    # Run specific test file
    python -m pytest tests/test_api_and_queue.py

    # Run with coverage
    python -m pytest tests/ --cov=src --cov-report=html
"""

__version__ = "1.0.0"
__author__ = "Agent Evaluation Tool Team"
