#!/usr/bin/env python3
"""Test script to verify configuration functionality."""

import os
import sys
import logging
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.core.config import get_config
from src.storage.db import get_storage


def test_logging_config():
    """Test logging configuration."""
    print("Testing logging configuration...")
    
    # Load config which should set up logging
    config = get_config()
    
    # Test logging
    logger = logging.getLogger("test_logger")
    logger.info("This is a test info message")
    logger.warning("This is a test warning message")
    logger.error("This is a test error message")
    
    # Check if log file was created
    log_file = config.get("logging.file", "./logs/agent_bench.log")
    log_path = Path(log_file)
    
    if log_path.exists():
        print(f"✓ Log file created: {log_path}")
        print(f"✓ Log file size: {log_path.stat().st_size} bytes")
    else:
        print(f"✗ Log file not found: {log_path}")


def test_storage_config():
    """Test storage configuration."""
    print("\nTesting storage configuration...")
    
    config = get_config()
    storage_type = config.get("storage.type", "sqlite")
    storage_path = config.get("storage.path", "./data/results.db")
    
    print(f"Storage type: {storage_type}")
    print(f"Storage path: {storage_path}")
    
    # Test storage initialization
    try:
        storage = get_storage()
        print("✓ Storage initialized successfully")
        
        # Test saving a dummy result
        test_result = {
            "id": "test_result_001",
            "benchmark": "test_benchmark",
            "model": "test_model",
            "task": "test_task",
            "score": 0.85,
            "execution_time": 1.23
        }
        
        import asyncio
        async def test_storage_operations():
            result_id = await storage.save_result(test_result)
            print(f"✓ Test result saved with ID: {result_id}")
            
            # Retrieve the result
            retrieved = await storage.get_result(result_id)
            if retrieved:
                print("✓ Test result retrieved successfully")
            else:
                print("✗ Failed to retrieve test result")
            
            # Clean up
            deleted = await storage.delete_result(result_id)
            if deleted:
                print("✓ Test result deleted successfully")
            else:
                print("✗ Failed to delete test result")
        
        asyncio.run(test_storage_operations())
        
    except Exception as e:
        print(f"✗ Storage test failed: {e}")


def test_api_config():
    """Test API configuration."""
    print("\nTesting API configuration...")
    
    config = get_config()
    api_config = config.get("api", {})
    
    host = api_config.get("host", "0.0.0.0")
    port = api_config.get("port", 8000)
    debug = api_config.get("debug", False)
    
    print(f"API host: {host}")
    print(f"API port: {port}")
    print(f"Debug mode: {debug}")
    
    print("✓ API configuration loaded successfully")


def test_worker_config():
    """Test worker configuration."""
    print("\nTesting worker configuration...")
    
    config = get_config()
    worker_config = config.get("worker", {})
    
    max_concurrent = worker_config.get("max_concurrent_tasks", 5)
    timeout = worker_config.get("timeout_seconds", 3600)
    collect_stats = worker_config.get("collect_stats", True)
    model_concurrency = worker_config.get("model_concurrency", {})
    
    print(f"Max concurrent tasks: {max_concurrent}")
    print(f"Timeout seconds: {timeout}")
    print(f"Collect stats: {collect_stats}")
    print(f"Model concurrency limits: {model_concurrency}")
    
    print("✓ Worker configuration loaded successfully")


def test_llm_config():
    """Test LLM configuration."""
    print("\nTesting LLM configuration...")
    
    config = get_config()
    llm_config = config.get("llm", {})
    
    default_timeout = llm_config.get("default_timeout", 60)
    max_retries = llm_config.get("max_retries", 3)
    retry_delay = llm_config.get("retry_delay", 2)
    endpoints = llm_config.get("endpoints", [])
    
    print(f"Default timeout: {default_timeout}")
    print(f"Max retries: {max_retries}")
    print(f"Retry delay: {retry_delay}")
    print(f"Number of endpoints: {len(endpoints)}")
    
    for i, endpoint in enumerate(endpoints):
        name = endpoint.get("name", f"endpoint_{i}")
        url = endpoint.get("url", "unknown")
        models = endpoint.get("models", [])
        print(f"  Endpoint {name}: {url} (models: {len(models)})")
    
    print("✓ LLM configuration loaded successfully")


def main():
    """Main test function."""
    print("Agent Evaluation Tool - Configuration Test")
    print("=" * 50)
    
    try:
        test_logging_config()
        test_storage_config()
        test_api_config()
        test_worker_config()
        test_llm_config()
        
        print("\n" + "=" * 50)
        print("✓ All configuration tests passed!")
        
    except Exception as e:
        print(f"\n✗ Configuration test failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
