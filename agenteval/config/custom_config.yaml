# Custom configuration for Agent Evaluation Tool
# Override default settings here

# API Configuration
api:
  port: 8080  # Custom port example

# LLM Service Configuration
llm:
  endpoints:
    - name: "custom-model-1"
      url: "http://localhost:8001/v1"
      api_key: ""  # Will be overridden by environment variable LLM_API_KEY_1 if set
    - name: "custom-model-2"
      url: "http://localhost:8002/v1"
      api_key: ""  # Will be overridden by environment variable LLM_API_KEY_2 if set

# Benchmark Configuration
benchmarks:
  tau_bench:
    # Custom tau-bench configurations
    specific_tasks: ["task1", "task2"]
  
  bfc:
    # Custom BFC configurations
    specific_tasks: ["function_calling_1", "function_calling_2"]
  
  gaia:
    # Custom GAIA configurations
    specific_tasks: ["reasoning_task_1", "planning_task_2"]

# Example of disabling a framework
frameworks:
  smol_agents:
    enabled: false

# Storage Configuration
storage:
  type: "json"  # Override to use JSON storage instead of SQLite
