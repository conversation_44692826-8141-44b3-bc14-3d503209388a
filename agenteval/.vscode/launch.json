{"version": "0.2.0", "configurations": [{"name": "Debug FastAPI App", "type": "debugpy", "request": "launch", "program": "${workspaceFolder}/main.py", "console": "integratedTerminal", "cwd": "${workspaceFolder}", "env": {"PYTHONPATH": "${workspaceFolder}", "CONFIG_PATH": "${workspaceFolder}/config/default.yaml"}, "args": ["--host", "127.0.0.1", "--port", "8000", "--reload"], "justMyCode": false, "stopOnEntry": false, "python": "/fs-computility/llmeval/zhudongsheng/miniconda3/envs/agenteval/bin/python"}, {"name": "Debug FastAPI App (Custom Config)", "type": "debugpy", "request": "launch", "program": "${workspaceFolder}/main.py", "console": "integratedTerminal", "cwd": "${workspaceFolder}", "env": {"PYTHONPATH": "${workspaceFolder}", "CONFIG_PATH": "${workspaceFolder}/config/custom_config.yaml"}, "args": ["--config", "${workspaceFolder}/config/custom_config.yaml", "--host", "127.0.0.1", "--port", "8000", "--reload"], "justMyCode": false, "stopOnEntry": false, "python": "/fs-computility/llmeval/zhudongsheng/miniconda3/envs/agenteval/bin/python"}, {"name": "Debug Worker", "type": "debugpy", "request": "launch", "program": "${workspaceFolder}/worker.py", "console": "integratedTerminal", "cwd": "${workspaceFolder}", "env": {"PYTHONPATH": "${workspaceFolder}", "CONFIG_PATH": "${workspaceFolder}/config/default.yaml"}, "args": ["--id", "debug-worker-1"], "justMyCode": false, "stopOnEntry": false, "python": "/fs-computility/llmeval/zhudongsheng/miniconda3/envs/agenteval/bin/python"}, {"name": "Debug Worker (Custom Config)", "type": "debugpy", "request": "launch", "program": "${workspaceFolder}/worker.py", "console": "integratedTerminal", "cwd": "${workspaceFolder}", "env": {"PYTHONPATH": "${workspaceFolder}", "CONFIG_PATH": "${workspaceFolder}/config/custom_config.yaml"}, "args": ["--id", "debug-worker-1", "--config", "${workspaceFolder}/config/custom_config.yaml"], "justMyCode": false, "stopOnEntry": false, "python": "${command:python.interpreterPath}"}, {"name": "Debug Tests (pytest)", "type": "debugpy", "request": "launch", "module": "pytest", "console": "integratedTerminal", "cwd": "${workspaceFolder}", "env": {"PYTHONPATH": "${workspaceFolder}"}, "args": ["tests/", "-v", "--tb=short"], "justMyCode": false, "stopOnEntry": false, "python": "/fs-computility/llmeval/zhudongsheng/miniconda3/envs/agenteval/bin/python"}, {"name": "Debug Current Test File", "type": "debugpy", "request": "launch", "module": "pytest", "console": "integratedTerminal", "cwd": "${workspaceFolder}", "env": {"PYTHONPATH": "${workspaceFolder}"}, "args": ["${file}", "-v", "--tb=short"], "justMyCode": false, "stopOnEntry": false, "python": "/fs-computility/llmeval/zhudongsheng/miniconda3/envs/agenteval/bin/python"}, {"name": "Debug Test Runner", "type": "debugpy", "request": "launch", "program": "${workspaceFolder}/run_tests.py", "console": "integratedTerminal", "cwd": "${workspaceFolder}", "env": {"PYTHONPATH": "${workspaceFolder}"}, "args": ["--verbose"], "justMyCode": false, "stopOnEntry": false, "python": "/fs-computility/llmeval/zhudongsheng/miniconda3/envs/agenteval/bin/python"}, {"name": "Debug Current Python File", "type": "debugpy", "request": "launch", "program": "${file}", "console": "integratedTerminal", "cwd": "${workspaceFolder}", "env": {"PYTHONPATH": "${workspaceFolder}"}, "justMyCode": false, "stopOnEntry": false, "python": "/fs-computility/llmeval/zhudongsheng/miniconda3/envs/agenteval/bin/python"}, {"name": "Attach to Process", "type": "debugpy", "request": "attach", "connect": {"host": "localhost", "port": 5678}, "justMyCode": false}, {"name": "Remote Attach", "type": "debugpy", "request": "attach", "connect": {"host": "localhost", "port": 5678}, "pathMappings": [{"localRoot": "${workspaceFolder}", "remoteRoot": "/app"}], "justMyCode": false}]}