2025-05-27 10:54:48,558 - src.core.logging_config - INFO - Logging configured: level=INFO, file=./logs/agent_bench.log, max_size=10MB, backups=5
2025-05-27 10:54:48,558 - test_logger - INFO - This is a test info message
2025-05-27 10:54:48,558 - test_logger - WARNING - This is a test warning message
2025-05-27 10:54:48,558 - test_logger - ERROR - This is a test error message
2025-05-27 10:54:48,570 - src.storage.db - INFO - Initialized SQLite database at data/results.db
2025-05-27 10:54:48,573 - src.storage.db - INFO - Saved result test_result_001 to SQLite database
2025-05-28 17:27:05,864 - src.core.logging_config - INFO - Logging configured: level=INFO, file=./logs/agent_bench.log, max_size=10MB, backups=5
2025-05-28 17:27:05,901 - src.llm.client - INFO - Initialized LLM client with 4 endpoints
2025-05-28 17:27:05,915 - src.llm.client - INFO - Initialized LLM client with 4 endpoints
2025-05-28 17:27:05,949 - src.llm.client - INFO - Initialized LLM client with 4 endpoints
2025-05-28 17:27:06,040 - httpx - INFO - HTTP Request: POST http://test/api/tasks "HTTP/1.1 200 OK"
2025-05-28 17:27:06,066 - httpx - INFO - HTTP Request: POST http://test/api/tasks/batch "HTTP/1.1 200 OK"
2025-05-28 17:27:06,085 - httpx - INFO - HTTP Request: GET http://test/api/tasks/test-task-123 "HTTP/1.1 200 OK"
2025-05-28 17:27:06,094 - httpx - INFO - HTTP Request: GET http://test/api/tasks/non-existent "HTTP/1.1 404 Not Found"
2025-05-28 17:27:06,117 - httpx - INFO - HTTP Request: GET http://test/api/tasks "HTTP/1.1 200 OK"
2025-05-28 17:27:06,126 - src.core.queue - INFO - Task 2e27f6f4-1c7a-446d-93a4-217085ba8411 (tau_bench) enqueued
2025-05-28 17:27:06,126 - httpx - INFO - HTTP Request: POST http://test/api/tasks "HTTP/1.1 200 OK"
2025-05-28 17:27:06,127 - httpx - INFO - HTTP Request: GET http://test/api/tasks/2e27f6f4-1c7a-446d-93a4-217085ba8411 "HTTP/1.1 200 OK"
2025-05-28 17:27:06,127 - src.core.queue - INFO - Task 2e27f6f4-1c7a-446d-93a4-217085ba8411 status updated to running
2025-05-28 17:27:06,127 - httpx - INFO - HTTP Request: GET http://test/api/tasks/2e27f6f4-1c7a-446d-93a4-217085ba8411 "HTTP/1.1 200 OK"
2025-05-28 17:27:06,127 - src.core.queue - INFO - Task 2e27f6f4-1c7a-446d-93a4-217085ba8411 status updated to completed
2025-05-28 17:27:06,127 - httpx - INFO - HTTP Request: GET http://test/api/tasks/2e27f6f4-1c7a-446d-93a4-217085ba8411 "HTTP/1.1 200 OK"
2025-05-28 17:27:06,128 - src.core.queue - INFO - Task 9e7aa9ff-44a0-4fc2-aff9-ec9d92e5658c (test_benchmark) enqueued
2025-05-28 17:27:06,128 - src.core.queue - INFO - Task 2e57b540-091b-4cb2-a5a7-0697877b2caf (test_benchmark) enqueued
2025-05-28 17:27:06,128 - src.core.queue - INFO - Task c8831273-b6a4-4b14-b7c0-91a2b55c9080 (test_benchmark) enqueued
2025-05-28 17:27:06,128 - src.core.queue - INFO - Task 0eb9f804-ffbd-472a-8957-24edf6cf04f3 (test_benchmark) enqueued
2025-05-28 17:27:06,128 - src.core.queue - INFO - Task 06df05aa-b894-4df3-8d12-1cf2a9dd6635 (test_benchmark) enqueued
2025-05-28 17:27:06,128 - src.core.queue - INFO - Task 0e89a473-0c3a-4830-a940-56f845bb19a6 (test_benchmark) enqueued
2025-05-28 17:27:06,128 - src.core.queue - INFO - Task 91df8677-923b-4146-8bb4-e1e11b5c80b8 (test_benchmark) enqueued
2025-05-28 17:27:06,128 - src.core.queue - INFO - Task cb44d01d-80db-4d56-8819-4a9b7712b0b3 (test_benchmark) enqueued
2025-05-28 17:27:06,128 - src.core.queue - INFO - Task f814f514-dd17-466c-b4eb-39bfd80b4092 (test_benchmark) enqueued
2025-05-28 17:27:06,129 - src.core.queue - INFO - Task b3ccf0e1-4ea4-4709-b0be-47cd69d98cd7 (test_benchmark) enqueued
2025-05-28 17:27:06,129 - src.core.queue - INFO - Task 9e7aa9ff-44a0-4fc2-aff9-ec9d92e5658c status updated to running
2025-05-28 17:27:06,129 - src.core.queue - INFO - Task 2e57b540-091b-4cb2-a5a7-0697877b2caf status updated to running
2025-05-28 17:27:06,129 - src.core.queue - INFO - Task c8831273-b6a4-4b14-b7c0-91a2b55c9080 status updated to running
2025-05-28 17:27:06,129 - src.core.queue - INFO - Task 0eb9f804-ffbd-472a-8957-24edf6cf04f3 status updated to running
2025-05-28 17:27:06,129 - src.core.queue - INFO - Task 06df05aa-b894-4df3-8d12-1cf2a9dd6635 status updated to running
2025-05-28 17:27:06,129 - src.core.queue - INFO - Task 0e89a473-0c3a-4830-a940-56f845bb19a6 status updated to running
2025-05-28 17:27:06,129 - src.core.queue - INFO - Task 91df8677-923b-4146-8bb4-e1e11b5c80b8 status updated to running
2025-05-28 17:27:06,129 - src.core.queue - INFO - Task cb44d01d-80db-4d56-8819-4a9b7712b0b3 status updated to running
2025-05-28 17:27:06,129 - src.core.queue - INFO - Task f814f514-dd17-466c-b4eb-39bfd80b4092 status updated to running
2025-05-28 17:27:06,129 - src.core.queue - INFO - Task b3ccf0e1-4ea4-4709-b0be-47cd69d98cd7 status updated to running
2025-05-28 17:27:06,140 - src.core.queue - INFO - Task 9e7aa9ff-44a0-4fc2-aff9-ec9d92e5658c status updated to completed
2025-05-28 17:27:06,140 - src.core.queue - INFO - Task 2e57b540-091b-4cb2-a5a7-0697877b2caf status updated to completed
2025-05-28 17:27:06,140 - src.core.queue - INFO - Task c8831273-b6a4-4b14-b7c0-91a2b55c9080 status updated to completed
2025-05-28 17:27:06,140 - src.core.queue - INFO - Task 0eb9f804-ffbd-472a-8957-24edf6cf04f3 status updated to completed
2025-05-28 17:27:06,140 - src.core.queue - INFO - Task 06df05aa-b894-4df3-8d12-1cf2a9dd6635 status updated to completed
2025-05-28 17:27:06,140 - src.core.queue - INFO - Task 0e89a473-0c3a-4830-a940-56f845bb19a6 status updated to completed
2025-05-28 17:27:06,140 - src.core.queue - INFO - Task 91df8677-923b-4146-8bb4-e1e11b5c80b8 status updated to completed
2025-05-28 17:27:06,140 - src.core.queue - INFO - Task cb44d01d-80db-4d56-8819-4a9b7712b0b3 status updated to completed
2025-05-28 17:27:06,140 - src.core.queue - INFO - Task f814f514-dd17-466c-b4eb-39bfd80b4092 status updated to completed
2025-05-28 17:27:06,140 - src.core.queue - INFO - Task b3ccf0e1-4ea4-4709-b0be-47cd69d98cd7 status updated to completed
2025-05-28 17:27:56,019 - src.core.logging_config - INFO - Logging configured: level=INFO, file=./logs/agent_bench.log, max_size=10MB, backups=5
2025-05-28 17:27:56,049 - src.llm.client - INFO - Initialized LLM client with 4 endpoints
2025-05-28 17:27:56,063 - src.llm.client - INFO - Initialized LLM client with 4 endpoints
2025-05-28 17:27:56,077 - src.llm.client - INFO - Initialized LLM client with 4 endpoints
2025-05-28 17:27:56,087 - httpx - INFO - HTTP Request: POST http://test/api/tasks "HTTP/1.1 200 OK"
2025-05-28 17:27:56,104 - httpx - INFO - HTTP Request: POST http://test/api/tasks/batch "HTTP/1.1 200 OK"
2025-05-28 17:27:56,109 - httpx - INFO - HTTP Request: GET http://test/api/tasks/test-task-123 "HTTP/1.1 200 OK"
2025-05-28 17:27:56,115 - httpx - INFO - HTTP Request: GET http://test/api/tasks/non-existent "HTTP/1.1 404 Not Found"
2025-05-28 17:27:56,121 - httpx - INFO - HTTP Request: GET http://test/api/tasks "HTTP/1.1 200 OK"
2025-05-28 17:27:56,125 - src.core.queue - INFO - Task 61266005-d1ec-46e0-a9ee-88692cbaf2b6 (tau_bench) enqueued
2025-05-28 17:27:56,126 - httpx - INFO - HTTP Request: POST http://test/api/tasks "HTTP/1.1 200 OK"
2025-05-28 17:27:56,126 - httpx - INFO - HTTP Request: GET http://test/api/tasks/61266005-d1ec-46e0-a9ee-88692cbaf2b6 "HTTP/1.1 200 OK"
2025-05-28 17:27:56,126 - src.core.queue - INFO - Task 61266005-d1ec-46e0-a9ee-88692cbaf2b6 status updated to running
2025-05-28 17:27:56,126 - httpx - INFO - HTTP Request: GET http://test/api/tasks/61266005-d1ec-46e0-a9ee-88692cbaf2b6 "HTTP/1.1 200 OK"
2025-05-28 17:27:56,126 - src.core.queue - INFO - Task 61266005-d1ec-46e0-a9ee-88692cbaf2b6 status updated to completed
2025-05-28 17:27:56,126 - httpx - INFO - HTTP Request: GET http://test/api/tasks/61266005-d1ec-46e0-a9ee-88692cbaf2b6 "HTTP/1.1 200 OK"
2025-05-28 17:27:56,127 - src.core.queue - INFO - Task aebc5214-307a-4651-a832-685ad90cde8b (test_benchmark) enqueued
2025-05-28 17:27:56,127 - src.core.queue - INFO - Task 91efe4b9-b9db-43e4-9fa8-ed981f0002c1 (test_benchmark) enqueued
2025-05-28 17:27:56,127 - src.core.queue - INFO - Task 8e67677c-fb50-4c54-b90c-21f7037bd9e0 (test_benchmark) enqueued
2025-05-28 17:27:56,127 - src.core.queue - INFO - Task 2029c0bf-b6c7-433b-96c9-c072653ce044 (test_benchmark) enqueued
2025-05-28 17:27:56,127 - src.core.queue - INFO - Task 9e9da07b-86b5-4dc3-8a55-08b0a23edaef (test_benchmark) enqueued
2025-05-28 17:27:56,127 - src.core.queue - INFO - Task 71af4f14-0458-4f1e-9eb8-6530d1f407d1 (test_benchmark) enqueued
2025-05-28 17:27:56,127 - src.core.queue - INFO - Task a5e395b7-847d-4add-8605-a62f9f1a8d76 (test_benchmark) enqueued
2025-05-28 17:27:56,127 - src.core.queue - INFO - Task b769a8bc-dd12-44f6-8c30-0a3ed869877f (test_benchmark) enqueued
2025-05-28 17:27:56,127 - src.core.queue - INFO - Task fd6f0630-5d4f-459a-86d0-32587119941c (test_benchmark) enqueued
2025-05-28 17:27:56,127 - src.core.queue - INFO - Task 33f5c522-24ae-40dc-b0fe-5948cfc629a5 (test_benchmark) enqueued
2025-05-28 17:27:56,127 - src.core.queue - INFO - Task aebc5214-307a-4651-a832-685ad90cde8b status updated to running
2025-05-28 17:27:56,127 - src.core.queue - INFO - Task 91efe4b9-b9db-43e4-9fa8-ed981f0002c1 status updated to running
2025-05-28 17:27:56,128 - src.core.queue - INFO - Task 8e67677c-fb50-4c54-b90c-21f7037bd9e0 status updated to running
2025-05-28 17:27:56,128 - src.core.queue - INFO - Task 2029c0bf-b6c7-433b-96c9-c072653ce044 status updated to running
2025-05-28 17:27:56,128 - src.core.queue - INFO - Task 9e9da07b-86b5-4dc3-8a55-08b0a23edaef status updated to running
2025-05-28 17:27:56,128 - src.core.queue - INFO - Task 71af4f14-0458-4f1e-9eb8-6530d1f407d1 status updated to running
2025-05-28 17:27:56,128 - src.core.queue - INFO - Task a5e395b7-847d-4add-8605-a62f9f1a8d76 status updated to running
2025-05-28 17:27:56,128 - src.core.queue - INFO - Task b769a8bc-dd12-44f6-8c30-0a3ed869877f status updated to running
2025-05-28 17:27:56,128 - src.core.queue - INFO - Task fd6f0630-5d4f-459a-86d0-32587119941c status updated to running
2025-05-28 17:27:56,128 - src.core.queue - INFO - Task 33f5c522-24ae-40dc-b0fe-5948cfc629a5 status updated to running
2025-05-28 17:27:56,139 - src.core.queue - INFO - Task aebc5214-307a-4651-a832-685ad90cde8b status updated to completed
2025-05-28 17:27:56,139 - src.core.queue - INFO - Task 91efe4b9-b9db-43e4-9fa8-ed981f0002c1 status updated to completed
2025-05-28 17:27:56,139 - src.core.queue - INFO - Task 8e67677c-fb50-4c54-b90c-21f7037bd9e0 status updated to completed
2025-05-28 17:27:56,139 - src.core.queue - INFO - Task 2029c0bf-b6c7-433b-96c9-c072653ce044 status updated to completed
2025-05-28 17:27:56,139 - src.core.queue - INFO - Task 9e9da07b-86b5-4dc3-8a55-08b0a23edaef status updated to completed
2025-05-28 17:27:56,139 - src.core.queue - INFO - Task 71af4f14-0458-4f1e-9eb8-6530d1f407d1 status updated to completed
2025-05-28 17:27:56,139 - src.core.queue - INFO - Task a5e395b7-847d-4add-8605-a62f9f1a8d76 status updated to completed
2025-05-28 17:27:56,139 - src.core.queue - INFO - Task b769a8bc-dd12-44f6-8c30-0a3ed869877f status updated to completed
2025-05-28 17:27:56,139 - src.core.queue - INFO - Task fd6f0630-5d4f-459a-86d0-32587119941c status updated to completed
2025-05-28 17:27:56,139 - src.core.queue - INFO - Task 33f5c522-24ae-40dc-b0fe-5948cfc629a5 status updated to completed
2025-05-28 17:36:31,286 - src.core.logging_config - INFO - Logging configured: level=INFO, file=./logs/agent_bench.log, max_size=10MB, backups=5
2025-05-28 17:36:31,317 - src.llm.client - INFO - Initialized LLM client with 4 endpoints
2025-05-28 17:36:31,331 - src.llm.client - INFO - Initialized LLM client with 4 endpoints
2025-05-28 17:36:31,346 - src.llm.client - INFO - Initialized LLM client with 4 endpoints
2025-05-28 17:36:31,368 - httpx - INFO - HTTP Request: POST http://test/api/tasks "HTTP/1.1 200 OK"
2025-05-28 17:36:31,374 - httpx - INFO - HTTP Request: POST http://test/api/tasks/batch "HTTP/1.1 200 OK"
2025-05-28 17:36:31,381 - httpx - INFO - HTTP Request: GET http://test/api/tasks/test-task-123 "HTTP/1.1 200 OK"
2025-05-28 17:36:31,386 - httpx - INFO - HTTP Request: GET http://test/api/tasks/non-existent "HTTP/1.1 404 Not Found"
2025-05-28 17:36:31,392 - httpx - INFO - HTTP Request: GET http://test/api/tasks "HTTP/1.1 200 OK"
2025-05-28 17:36:31,398 - src.core.queue - INFO - Task 63780764-26fd-460c-9243-982e894a1557 (tau_bench) enqueued
2025-05-28 17:36:31,398 - httpx - INFO - HTTP Request: POST http://test/api/tasks "HTTP/1.1 200 OK"
2025-05-28 17:36:31,398 - httpx - INFO - HTTP Request: GET http://test/api/tasks/63780764-26fd-460c-9243-982e894a1557 "HTTP/1.1 200 OK"
2025-05-28 17:36:31,398 - src.core.queue - INFO - Task 63780764-26fd-460c-9243-982e894a1557 status updated to running
2025-05-28 17:36:31,399 - httpx - INFO - HTTP Request: GET http://test/api/tasks/63780764-26fd-460c-9243-982e894a1557 "HTTP/1.1 200 OK"
2025-05-28 17:36:31,399 - src.core.queue - INFO - Task 63780764-26fd-460c-9243-982e894a1557 status updated to completed
2025-05-28 17:36:31,399 - httpx - INFO - HTTP Request: GET http://test/api/tasks/63780764-26fd-460c-9243-982e894a1557 "HTTP/1.1 200 OK"
2025-05-28 17:36:31,399 - src.core.queue - INFO - Task 1587af54-18d4-441e-9826-e0f360caa205 (test_benchmark) enqueued
2025-05-28 17:36:31,400 - src.core.queue - INFO - Task dca77885-445a-4218-b830-842b56378dbc (test_benchmark) enqueued
2025-05-28 17:36:31,400 - src.core.queue - INFO - Task 605ae85e-2395-48cd-ab27-45b41ce336ba (test_benchmark) enqueued
2025-05-28 17:36:31,400 - src.core.queue - INFO - Task 8469ddf2-8cb0-4097-95c0-0546269e04f0 (test_benchmark) enqueued
2025-05-28 17:36:31,400 - src.core.queue - INFO - Task 7869c93d-dfbf-4072-85c8-ee90b6ead84c (test_benchmark) enqueued
2025-05-28 17:36:31,400 - src.core.queue - INFO - Task c4adfeb1-d61e-426e-b4a8-2d2db437c12c (test_benchmark) enqueued
2025-05-28 17:36:31,400 - src.core.queue - INFO - Task 99177d92-fde3-41fa-ba38-ea4b384415b3 (test_benchmark) enqueued
2025-05-28 17:36:31,400 - src.core.queue - INFO - Task 4b0c8819-02db-40a5-868c-2e4f805cc049 (test_benchmark) enqueued
2025-05-28 17:36:31,400 - src.core.queue - INFO - Task 0c08e43c-dc31-4c0c-8e37-fe68236b4723 (test_benchmark) enqueued
2025-05-28 17:36:31,400 - src.core.queue - INFO - Task 36f1c364-d007-443e-b840-dd49e68410e3 (test_benchmark) enqueued
2025-05-28 17:36:31,400 - src.core.queue - INFO - Task 1587af54-18d4-441e-9826-e0f360caa205 status updated to running
2025-05-28 17:36:31,400 - src.core.queue - INFO - Task dca77885-445a-4218-b830-842b56378dbc status updated to running
2025-05-28 17:36:31,400 - src.core.queue - INFO - Task 605ae85e-2395-48cd-ab27-45b41ce336ba status updated to running
2025-05-28 17:36:31,400 - src.core.queue - INFO - Task 8469ddf2-8cb0-4097-95c0-0546269e04f0 status updated to running
2025-05-28 17:36:31,400 - src.core.queue - INFO - Task 7869c93d-dfbf-4072-85c8-ee90b6ead84c status updated to running
2025-05-28 17:36:31,400 - src.core.queue - INFO - Task c4adfeb1-d61e-426e-b4a8-2d2db437c12c status updated to running
2025-05-28 17:36:31,400 - src.core.queue - INFO - Task 99177d92-fde3-41fa-ba38-ea4b384415b3 status updated to running
2025-05-28 17:36:31,400 - src.core.queue - INFO - Task 4b0c8819-02db-40a5-868c-2e4f805cc049 status updated to running
2025-05-28 17:36:31,400 - src.core.queue - INFO - Task 0c08e43c-dc31-4c0c-8e37-fe68236b4723 status updated to running
2025-05-28 17:36:31,400 - src.core.queue - INFO - Task 36f1c364-d007-443e-b840-dd49e68410e3 status updated to running
2025-05-28 17:36:31,411 - src.core.queue - INFO - Task 1587af54-18d4-441e-9826-e0f360caa205 status updated to completed
2025-05-28 17:36:31,411 - src.core.queue - INFO - Task dca77885-445a-4218-b830-842b56378dbc status updated to completed
2025-05-28 17:36:31,411 - src.core.queue - INFO - Task 605ae85e-2395-48cd-ab27-45b41ce336ba status updated to completed
2025-05-28 17:36:31,411 - src.core.queue - INFO - Task 8469ddf2-8cb0-4097-95c0-0546269e04f0 status updated to completed
2025-05-28 17:36:31,411 - src.core.queue - INFO - Task 7869c93d-dfbf-4072-85c8-ee90b6ead84c status updated to completed
2025-05-28 17:36:31,411 - src.core.queue - INFO - Task c4adfeb1-d61e-426e-b4a8-2d2db437c12c status updated to completed
2025-05-28 17:36:31,411 - src.core.queue - INFO - Task 99177d92-fde3-41fa-ba38-ea4b384415b3 status updated to completed
2025-05-28 17:36:31,411 - src.core.queue - INFO - Task 4b0c8819-02db-40a5-868c-2e4f805cc049 status updated to completed
2025-05-28 17:36:31,411 - src.core.queue - INFO - Task 0c08e43c-dc31-4c0c-8e37-fe68236b4723 status updated to completed
2025-05-28 17:36:31,411 - src.core.queue - INFO - Task 36f1c364-d007-443e-b840-dd49e68410e3 status updated to completed
2025-05-28 17:37:57,741 - src.core.logging_config - INFO - Logging configured: level=INFO, file=./logs/agent_bench.log, max_size=10MB, backups=5
2025-05-28 17:37:57,775 - src.llm.client - INFO - Initialized LLM client with 4 endpoints
2025-05-28 17:37:57,791 - src.llm.client - INFO - Initialized LLM client with 4 endpoints
2025-05-28 17:37:57,807 - src.llm.client - INFO - Initialized LLM client with 4 endpoints
2025-05-28 17:37:57,821 - httpx - INFO - HTTP Request: POST http://test/api/tasks "HTTP/1.1 200 OK"
2025-05-28 17:37:57,828 - httpx - INFO - HTTP Request: POST http://test/api/tasks/batch "HTTP/1.1 200 OK"
2025-05-28 17:37:57,836 - httpx - INFO - HTTP Request: GET http://test/api/tasks/test-task-123 "HTTP/1.1 200 OK"
2025-05-28 17:37:57,842 - httpx - INFO - HTTP Request: GET http://test/api/tasks/non-existent "HTTP/1.1 404 Not Found"
2025-05-28 17:37:57,847 - httpx - INFO - HTTP Request: GET http://test/api/tasks "HTTP/1.1 200 OK"
2025-05-28 17:37:57,856 - src.core.queue - INFO - Task 1134a5a6-691e-4efa-9cee-fc382026898e (tau_bench) enqueued
2025-05-28 17:37:57,856 - httpx - INFO - HTTP Request: POST http://test/api/tasks "HTTP/1.1 200 OK"
2025-05-28 17:37:57,856 - httpx - INFO - HTTP Request: GET http://test/api/tasks/1134a5a6-691e-4efa-9cee-fc382026898e "HTTP/1.1 200 OK"
2025-05-28 17:37:57,856 - src.core.queue - INFO - Task 1134a5a6-691e-4efa-9cee-fc382026898e status updated to running
2025-05-28 17:37:57,856 - httpx - INFO - HTTP Request: GET http://test/api/tasks/1134a5a6-691e-4efa-9cee-fc382026898e "HTTP/1.1 200 OK"
2025-05-28 17:37:57,856 - src.core.queue - INFO - Task 1134a5a6-691e-4efa-9cee-fc382026898e status updated to completed
2025-05-28 17:37:57,857 - httpx - INFO - HTTP Request: GET http://test/api/tasks/1134a5a6-691e-4efa-9cee-fc382026898e "HTTP/1.1 200 OK"
2025-05-28 17:37:57,857 - src.core.queue - INFO - Task 617fa842-a029-48e6-9a80-8b475ac0a877 (test_benchmark) enqueued
2025-05-28 17:37:57,857 - src.core.queue - INFO - Task 8a56e8dc-b878-4fbb-bab0-ae691cc7719b (test_benchmark) enqueued
2025-05-28 17:37:57,857 - src.core.queue - INFO - Task 7666038f-b52f-48c3-a9b5-e8af7cb0f276 (test_benchmark) enqueued
2025-05-28 17:37:57,857 - src.core.queue - INFO - Task c9389b74-5d60-442c-81d3-7f4b88b44862 (test_benchmark) enqueued
2025-05-28 17:37:57,857 - src.core.queue - INFO - Task 6aefd1fd-ebf4-4c45-b7c6-2fcd8fbcad0e (test_benchmark) enqueued
2025-05-28 17:37:57,857 - src.core.queue - INFO - Task fa665789-ae80-404e-a41f-09367f6fef41 (test_benchmark) enqueued
2025-05-28 17:37:57,858 - src.core.queue - INFO - Task e32ff648-1c0a-4d20-9533-0e8166cf5619 (test_benchmark) enqueued
2025-05-28 17:37:57,858 - src.core.queue - INFO - Task f7e2bef3-09d3-4e30-b220-9f4d9048f542 (test_benchmark) enqueued
2025-05-28 17:37:57,858 - src.core.queue - INFO - Task aba3ce3a-1e4a-4202-90f7-c3c25e3cada5 (test_benchmark) enqueued
2025-05-28 17:37:57,858 - src.core.queue - INFO - Task 3d1900b6-f0f5-4807-af27-da4cfc00928b (test_benchmark) enqueued
2025-05-28 17:37:57,858 - src.core.queue - INFO - Task 617fa842-a029-48e6-9a80-8b475ac0a877 status updated to running
2025-05-28 17:37:57,858 - src.core.queue - INFO - Task 8a56e8dc-b878-4fbb-bab0-ae691cc7719b status updated to running
2025-05-28 17:37:57,858 - src.core.queue - INFO - Task 7666038f-b52f-48c3-a9b5-e8af7cb0f276 status updated to running
2025-05-28 17:37:57,858 - src.core.queue - INFO - Task c9389b74-5d60-442c-81d3-7f4b88b44862 status updated to running
2025-05-28 17:37:57,858 - src.core.queue - INFO - Task 6aefd1fd-ebf4-4c45-b7c6-2fcd8fbcad0e status updated to running
2025-05-28 17:37:57,858 - src.core.queue - INFO - Task fa665789-ae80-404e-a41f-09367f6fef41 status updated to running
2025-05-28 17:37:57,858 - src.core.queue - INFO - Task e32ff648-1c0a-4d20-9533-0e8166cf5619 status updated to running
2025-05-28 17:37:57,858 - src.core.queue - INFO - Task f7e2bef3-09d3-4e30-b220-9f4d9048f542 status updated to running
2025-05-28 17:37:57,858 - src.core.queue - INFO - Task aba3ce3a-1e4a-4202-90f7-c3c25e3cada5 status updated to running
2025-05-28 17:37:57,858 - src.core.queue - INFO - Task 3d1900b6-f0f5-4807-af27-da4cfc00928b status updated to running
2025-05-28 17:37:57,869 - src.core.queue - INFO - Task 617fa842-a029-48e6-9a80-8b475ac0a877 status updated to completed
2025-05-28 17:37:57,869 - src.core.queue - INFO - Task 8a56e8dc-b878-4fbb-bab0-ae691cc7719b status updated to completed
2025-05-28 17:37:57,869 - src.core.queue - INFO - Task 7666038f-b52f-48c3-a9b5-e8af7cb0f276 status updated to completed
2025-05-28 17:37:57,869 - src.core.queue - INFO - Task c9389b74-5d60-442c-81d3-7f4b88b44862 status updated to completed
2025-05-28 17:37:57,869 - src.core.queue - INFO - Task 6aefd1fd-ebf4-4c45-b7c6-2fcd8fbcad0e status updated to completed
2025-05-28 17:37:57,869 - src.core.queue - INFO - Task fa665789-ae80-404e-a41f-09367f6fef41 status updated to completed
2025-05-28 17:37:57,869 - src.core.queue - INFO - Task e32ff648-1c0a-4d20-9533-0e8166cf5619 status updated to completed
2025-05-28 17:37:57,869 - src.core.queue - INFO - Task f7e2bef3-09d3-4e30-b220-9f4d9048f542 status updated to completed
2025-05-28 17:37:57,869 - src.core.queue - INFO - Task aba3ce3a-1e4a-4202-90f7-c3c25e3cada5 status updated to completed
2025-05-28 17:37:57,869 - src.core.queue - INFO - Task 3d1900b6-f0f5-4807-af27-da4cfc00928b status updated to completed
2025-05-29 16:37:34,640 - src.core.logging_config - INFO - Logging configured: level=INFO, file=./logs/agent_bench.log, max_size=10MB, backups=5
2025-05-29 16:37:34,674 - src.llm.client - INFO - Initialized LLM client with 4 endpoints
2025-05-29 16:37:34,689 - src.llm.client - INFO - Initialized LLM client with 4 endpoints
2025-05-29 16:37:34,704 - src.llm.client - INFO - Initialized LLM client with 4 endpoints
2025-05-29 16:37:34,718 - httpx - INFO - HTTP Request: POST http://test/api/tasks "HTTP/1.1 200 OK"
2025-05-29 16:37:34,725 - httpx - INFO - HTTP Request: POST http://test/api/tasks/batch "HTTP/1.1 200 OK"
2025-05-29 16:37:34,732 - httpx - INFO - HTTP Request: GET http://test/api/tasks/test-task-123 "HTTP/1.1 200 OK"
2025-05-29 16:37:34,738 - httpx - INFO - HTTP Request: GET http://test/api/tasks/non-existent "HTTP/1.1 404 Not Found"
2025-05-29 16:37:34,743 - httpx - INFO - HTTP Request: GET http://test/api/tasks "HTTP/1.1 200 OK"
2025-05-29 16:37:34,750 - src.core.queue - INFO - Task d93012b5-edd0-47b7-81bf-3aced682a6f9 (tau_bench) enqueued
2025-05-29 16:37:34,750 - httpx - INFO - HTTP Request: POST http://test/api/tasks "HTTP/1.1 200 OK"
2025-05-29 16:37:34,750 - httpx - INFO - HTTP Request: GET http://test/api/tasks/d93012b5-edd0-47b7-81bf-3aced682a6f9 "HTTP/1.1 200 OK"
2025-05-29 16:37:34,750 - src.core.queue - INFO - Task d93012b5-edd0-47b7-81bf-3aced682a6f9 status updated to running
2025-05-29 16:37:34,750 - httpx - INFO - HTTP Request: GET http://test/api/tasks/d93012b5-edd0-47b7-81bf-3aced682a6f9 "HTTP/1.1 200 OK"
2025-05-29 16:37:34,751 - src.core.queue - INFO - Task d93012b5-edd0-47b7-81bf-3aced682a6f9 status updated to completed
2025-05-29 16:37:34,751 - httpx - INFO - HTTP Request: GET http://test/api/tasks/d93012b5-edd0-47b7-81bf-3aced682a6f9 "HTTP/1.1 200 OK"
2025-05-29 16:37:34,751 - src.core.queue - INFO - Task 4f8c0bf6-6a82-446d-812c-e76e6209fb4b (test_benchmark) enqueued
2025-05-29 16:37:34,752 - src.core.queue - INFO - Task 2d3d09d3-453e-42e1-a97c-0a651f26f3f3 (test_benchmark) enqueued
2025-05-29 16:37:34,752 - src.core.queue - INFO - Task 2cf865a4-826b-4a14-8327-8c3a7e38cb52 (test_benchmark) enqueued
2025-05-29 16:37:34,752 - src.core.queue - INFO - Task 68b91202-d45c-4020-a8f9-579e6d893b3d (test_benchmark) enqueued
2025-05-29 16:37:34,752 - src.core.queue - INFO - Task 8951712f-f42f-4730-ac04-fa46195f1e3f (test_benchmark) enqueued
2025-05-29 16:37:34,752 - src.core.queue - INFO - Task b7152830-59c4-47e9-b6d5-8a301104b1ad (test_benchmark) enqueued
2025-05-29 16:37:34,752 - src.core.queue - INFO - Task 5b670f24-c8d9-4fed-adce-82055022a676 (test_benchmark) enqueued
2025-05-29 16:37:34,752 - src.core.queue - INFO - Task 87167096-1d9a-4937-8de8-83558783e9cf (test_benchmark) enqueued
2025-05-29 16:37:34,752 - src.core.queue - INFO - Task 84b2db09-e0d9-4b91-8c65-738137d0513b (test_benchmark) enqueued
2025-05-29 16:37:34,752 - src.core.queue - INFO - Task 07e10b5c-59b6-4dea-9523-c4b9ef3839b6 (test_benchmark) enqueued
2025-05-29 16:37:34,752 - src.core.queue - INFO - Task 4f8c0bf6-6a82-446d-812c-e76e6209fb4b status updated to running
2025-05-29 16:37:34,752 - src.core.queue - INFO - Task 2d3d09d3-453e-42e1-a97c-0a651f26f3f3 status updated to running
2025-05-29 16:37:34,752 - src.core.queue - INFO - Task 2cf865a4-826b-4a14-8327-8c3a7e38cb52 status updated to running
2025-05-29 16:37:34,752 - src.core.queue - INFO - Task 68b91202-d45c-4020-a8f9-579e6d893b3d status updated to running
2025-05-29 16:37:34,752 - src.core.queue - INFO - Task 8951712f-f42f-4730-ac04-fa46195f1e3f status updated to running
2025-05-29 16:37:34,752 - src.core.queue - INFO - Task b7152830-59c4-47e9-b6d5-8a301104b1ad status updated to running
2025-05-29 16:37:34,752 - src.core.queue - INFO - Task 5b670f24-c8d9-4fed-adce-82055022a676 status updated to running
2025-05-29 16:37:34,752 - src.core.queue - INFO - Task 87167096-1d9a-4937-8de8-83558783e9cf status updated to running
2025-05-29 16:37:34,752 - src.core.queue - INFO - Task 84b2db09-e0d9-4b91-8c65-738137d0513b status updated to running
2025-05-29 16:37:34,752 - src.core.queue - INFO - Task 07e10b5c-59b6-4dea-9523-c4b9ef3839b6 status updated to running
2025-05-29 16:37:34,763 - src.core.queue - INFO - Task 4f8c0bf6-6a82-446d-812c-e76e6209fb4b status updated to completed
2025-05-29 16:37:34,763 - src.core.queue - INFO - Task 2d3d09d3-453e-42e1-a97c-0a651f26f3f3 status updated to completed
2025-05-29 16:37:34,763 - src.core.queue - INFO - Task 2cf865a4-826b-4a14-8327-8c3a7e38cb52 status updated to completed
2025-05-29 16:37:34,763 - src.core.queue - INFO - Task 68b91202-d45c-4020-a8f9-579e6d893b3d status updated to completed
2025-05-29 16:37:34,763 - src.core.queue - INFO - Task 8951712f-f42f-4730-ac04-fa46195f1e3f status updated to completed
2025-05-29 16:37:34,763 - src.core.queue - INFO - Task b7152830-59c4-47e9-b6d5-8a301104b1ad status updated to completed
2025-05-29 16:37:34,763 - src.core.queue - INFO - Task 5b670f24-c8d9-4fed-adce-82055022a676 status updated to completed
2025-05-29 16:37:34,763 - src.core.queue - INFO - Task 87167096-1d9a-4937-8de8-83558783e9cf status updated to completed
2025-05-29 16:37:34,763 - src.core.queue - INFO - Task 84b2db09-e0d9-4b91-8c65-738137d0513b status updated to completed
2025-05-29 16:37:34,763 - src.core.queue - INFO - Task 07e10b5c-59b6-4dea-9523-c4b9ef3839b6 status updated to completed
2025-05-29 16:38:08,938 - src.core.logging_config - INFO - Logging configured: level=INFO, file=./logs/agent_bench.log, max_size=10MB, backups=5
2025-05-29 16:38:08,976 - src.llm.client - INFO - Initialized LLM client with 4 endpoints
2025-05-29 16:38:08,993 - src.llm.client - INFO - Initialized LLM client with 4 endpoints
2025-05-29 16:38:09,009 - src.llm.client - INFO - Initialized LLM client with 4 endpoints
2025-05-29 16:38:09,030 - httpx - INFO - HTTP Request: POST http://test/api/tasks "HTTP/1.1 200 OK"
2025-05-29 16:38:09,040 - httpx - INFO - HTTP Request: POST http://test/api/tasks/batch "HTTP/1.1 200 OK"
2025-05-29 16:38:09,061 - httpx - INFO - HTTP Request: GET http://test/api/tasks/test-task-123 "HTTP/1.1 200 OK"
2025-05-29 16:38:09,070 - httpx - INFO - HTTP Request: GET http://test/api/tasks/non-existent "HTTP/1.1 404 Not Found"
2025-05-29 16:38:09,080 - httpx - INFO - HTTP Request: GET http://test/api/tasks "HTTP/1.1 200 OK"
2025-05-29 16:38:09,088 - src.core.queue - INFO - Task 0ffb3461-bec2-4462-8679-bc711f899f22 (tau_bench) enqueued
2025-05-29 16:38:09,089 - httpx - INFO - HTTP Request: POST http://test/api/tasks "HTTP/1.1 200 OK"
2025-05-29 16:38:09,089 - httpx - INFO - HTTP Request: GET http://test/api/tasks/0ffb3461-bec2-4462-8679-bc711f899f22 "HTTP/1.1 200 OK"
2025-05-29 16:38:09,089 - src.core.queue - INFO - Task 0ffb3461-bec2-4462-8679-bc711f899f22 status updated to running
2025-05-29 16:38:09,091 - httpx - INFO - HTTP Request: GET http://test/api/tasks/0ffb3461-bec2-4462-8679-bc711f899f22 "HTTP/1.1 200 OK"
2025-05-29 16:38:09,091 - src.core.queue - INFO - Task 0ffb3461-bec2-4462-8679-bc711f899f22 status updated to completed
2025-05-29 16:38:09,091 - httpx - INFO - HTTP Request: GET http://test/api/tasks/0ffb3461-bec2-4462-8679-bc711f899f22 "HTTP/1.1 200 OK"
2025-05-29 16:38:09,092 - src.core.queue - INFO - Task a3d0c5bf-e612-489d-af2a-4ed3bf093913 (test_benchmark) enqueued
2025-05-29 16:38:09,093 - src.core.queue - INFO - Task f365e168-5440-428d-9665-9b009b2e9fed (test_benchmark) enqueued
2025-05-29 16:38:09,093 - src.core.queue - INFO - Task 153104c1-7b41-4aaf-b09d-cb9006e6e588 (test_benchmark) enqueued
2025-05-29 16:38:09,093 - src.core.queue - INFO - Task 639a94ad-cbe2-4abb-949f-74fa6b84c050 (test_benchmark) enqueued
2025-05-29 16:38:09,093 - src.core.queue - INFO - Task c9df82e3-f177-4c35-917e-6a89ea37dd7e (test_benchmark) enqueued
2025-05-29 16:38:09,093 - src.core.queue - INFO - Task 84c5952d-bba1-4042-9ccc-9157fddbccca (test_benchmark) enqueued
2025-05-29 16:38:09,093 - src.core.queue - INFO - Task c31717f9-8b22-4629-88f3-084976f6ad91 (test_benchmark) enqueued
2025-05-29 16:38:09,093 - src.core.queue - INFO - Task 1a0df2d3-e237-4dd5-8da8-08b1817a3e42 (test_benchmark) enqueued
2025-05-29 16:38:09,093 - src.core.queue - INFO - Task 4e81b120-36ff-4ca0-8365-75bc828e71b6 (test_benchmark) enqueued
2025-05-29 16:38:09,093 - src.core.queue - INFO - Task 4f1306e4-aa0a-4e7f-98ab-c7e0df92dd8f (test_benchmark) enqueued
2025-05-29 16:38:09,093 - src.core.queue - INFO - Task a3d0c5bf-e612-489d-af2a-4ed3bf093913 status updated to running
2025-05-29 16:38:09,093 - src.core.queue - INFO - Task f365e168-5440-428d-9665-9b009b2e9fed status updated to running
2025-05-29 16:38:09,093 - src.core.queue - INFO - Task 153104c1-7b41-4aaf-b09d-cb9006e6e588 status updated to running
2025-05-29 16:38:09,093 - src.core.queue - INFO - Task 639a94ad-cbe2-4abb-949f-74fa6b84c050 status updated to running
2025-05-29 16:38:09,093 - src.core.queue - INFO - Task c9df82e3-f177-4c35-917e-6a89ea37dd7e status updated to running
2025-05-29 16:38:09,093 - src.core.queue - INFO - Task 84c5952d-bba1-4042-9ccc-9157fddbccca status updated to running
2025-05-29 16:38:09,093 - src.core.queue - INFO - Task c31717f9-8b22-4629-88f3-084976f6ad91 status updated to running
2025-05-29 16:38:09,093 - src.core.queue - INFO - Task 1a0df2d3-e237-4dd5-8da8-08b1817a3e42 status updated to running
2025-05-29 16:38:09,093 - src.core.queue - INFO - Task 4e81b120-36ff-4ca0-8365-75bc828e71b6 status updated to running
2025-05-29 16:38:09,094 - src.core.queue - INFO - Task 4f1306e4-aa0a-4e7f-98ab-c7e0df92dd8f status updated to running
2025-05-29 16:38:09,104 - src.core.queue - INFO - Task a3d0c5bf-e612-489d-af2a-4ed3bf093913 status updated to completed
2025-05-29 16:38:09,104 - src.core.queue - INFO - Task f365e168-5440-428d-9665-9b009b2e9fed status updated to completed
2025-05-29 16:38:09,104 - src.core.queue - INFO - Task 153104c1-7b41-4aaf-b09d-cb9006e6e588 status updated to completed
2025-05-29 16:38:09,104 - src.core.queue - INFO - Task 639a94ad-cbe2-4abb-949f-74fa6b84c050 status updated to completed
2025-05-29 16:38:09,104 - src.core.queue - INFO - Task c9df82e3-f177-4c35-917e-6a89ea37dd7e status updated to completed
2025-05-29 16:38:09,105 - src.core.queue - INFO - Task 84c5952d-bba1-4042-9ccc-9157fddbccca status updated to completed
2025-05-29 16:38:09,105 - src.core.queue - INFO - Task c31717f9-8b22-4629-88f3-084976f6ad91 status updated to completed
2025-05-29 16:38:09,105 - src.core.queue - INFO - Task 1a0df2d3-e237-4dd5-8da8-08b1817a3e42 status updated to completed
2025-05-29 16:38:09,105 - src.core.queue - INFO - Task 4e81b120-36ff-4ca0-8365-75bc828e71b6 status updated to completed
2025-05-29 16:38:09,105 - src.core.queue - INFO - Task 4f1306e4-aa0a-4e7f-98ab-c7e0df92dd8f status updated to completed
2025-06-03 09:59:03,406 - src.core.logging_config - INFO - Logging configured: level=INFO, file=./logs/agent_bench.log, max_size=10MB, backups=5
2025-06-03 11:24:23,940 - src.core.logging_config - INFO - Logging configured: level=INFO, file=./logs/agent_bench.log, max_size=10MB, backups=5
2025-06-03 11:32:44,276 - src.core.logging_config - INFO - Logging configured: level=INFO, file=./logs/agent_bench.log, max_size=10MB, backups=5
2025-06-03 11:33:04,681 - src.core.logging_config - INFO - Logging configured: level=INFO, file=./logs/agent_bench.log, max_size=10MB, backups=5
